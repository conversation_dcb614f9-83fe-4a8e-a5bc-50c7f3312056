module.exports = async function volleyStationEventAccess(req, res, next) {
    const eventId = parseInt(req.params.eventId);

    if (!eventId || isNaN(eventId)) {
        return res.validation('Invalid event identifier passed');
    }

    try {
        const hasAccess = await hasVolleyStationAccess(eventId);
        if (!hasAccess) {
            return res.status(403).json({
                error: 'Event not available for VolleyStation integration'
            });
        }
        next();
    } catch (err) {
        sails.log.error('VolleyStation event access error:', err);
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Unable to verify event access'
        });
    }
};

async function hasVolleyStationAccess(eventId) {
    const eventQuery = knex('event as e')
        .select('event_id')
        .where('event_id', eventId)
        .where('enable_volley_station', true);

    const { rows } = await Db.query(eventQuery);
    return rows.length > 0;
}
